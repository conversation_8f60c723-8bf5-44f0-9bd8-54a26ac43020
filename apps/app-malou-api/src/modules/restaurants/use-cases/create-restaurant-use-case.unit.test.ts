import 'reflect-metadata';

import { container } from 'tsyringe';

import { CreateRestaurantBodyDto } from '@malou-io/package-dto';
import { newDbId } from '@malou-io/package-models';
import { BusinessCategory, MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { InjectionToken } from ':helpers/injection';
import ':helpers/tests/testing-utils';
import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import LabelsUseCases from ':modules/labels/labels.use-cases';
import PlatformInsightsRepository from ':modules/platform-insights/platform-insights.repository';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { PlatformsUseCases } from ':modules/platforms/platforms.use-cases';
import { KeywordSearchImpressionsService } from ':modules/platforms/services/keyword-search-impressions/keyword-search-impressions.service';
import ReportsRepository from ':modules/reports/reports.repository';
import { RestaurantAiSettingsRepository } from ':modules/restaurant-ai-settings/restaurant-ai-settings.repository';
import { RestaurantsDtoMapper } from ':modules/restaurants/mappers/restaurants.dto-mapper';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import RestaurantsUseCases from ':modules/restaurants/restaurants.use-cases';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import YextActivateLocationUseCase from ':modules/restaurants/use-cases/activate-yext-location.use-case';
import AddDefaultReviewAutomationsUseCase from ':modules/restaurants/use-cases/add-default-review-automations.use-case';
import { CreateRestaurantUseCase } from ':modules/restaurants/use-cases/create-restaurant.use-case';
import { getDefaultUser } from ':modules/users/tests/user.builder';
import UsersUseCases from ':modules/users/users.use-cases';

describe('CreateRestaurantUseCase', () => {
    beforeAll(() => {
        registerRepositories(['RestaurantsRepository', 'UsersRepository', 'OrganizationsRepository']);
    });

    // Mock classes for dependencies
    class MockPlatformsUseCases {
        async getLocationDataForPlatform() {
            return {
                name: 'Test Restaurant',
                socialId: 'test-social-id',
                address: { formattedAddress: 'Test Address' },
            };
        }

        async initializePlatform() {
            return {
                _id: newDbId(),
                rating: 4.5,
                socialId: 'test-social-id',
                key: PlatformKey.GMB,
            };
        }
    }

    class MockRestaurantsUseCases {
        async userCanCreate() {
            return true;
        }

        async upsertRestaurant() {
            return getDefaultRestaurant()._id(newDbId()).build();
        }

        async addRestaurantForUser() {
            return {};
        }

        async addRestaurantForAppAdmin() {
            return {};
        }
    }

    class MockLabelsUseCases {
        async initDefaultLabels() {
            return {};
        }
    }

    class MockUsersUseCases {
        async forceExpireAbilitySession() {
            return {};
        }
    }

    class MockYextActivateLocationUseCase {
        async execute() {
            return {};
        }
    }

    class MockAddDefaultReviewAutomationsUseCase {
        async execute() {
            return {};
        }
    }

    class MockRestaurantsDtoMapper {
        toCreateRestaurantDto(restaurant: any) {
            return {
                _id: restaurant._id.toString(),
                name: restaurant.name,
            };
        }
    }

    class MockKeywordSearchImpressionsService {
        createMessageQueueToFetchMonthlyKeywordSearchImpressions() {
            return Promise.resolve();
        }
    }

    // Mock repositories
    class MockPlatformInsightsRepository {
        async upsert() {
            return {};
        }
    }

    class MockReportsRepository {
        async addRestaurantForUser() {
            return {};
        }
    }

    class MockRestaurantAiSettingsRepository {
        async createDefaultRestaurantAiSettings() {
            return {};
        }
    }

    class MockPlatformsRepository {
        async upsert() {
            return {};
        }
    }

    beforeEach(() => {
        // Register all mock dependencies
        container.register(PlatformsUseCases, { useValue: new MockPlatformsUseCases() as any });
        container.register(RestaurantsUseCases, { useValue: new MockRestaurantsUseCases() as any });
        container.register(LabelsUseCases, { useValue: new MockLabelsUseCases() as any });
        container.register(UsersUseCases, { useValue: new MockUsersUseCases() as any });
        container.register(YextActivateLocationUseCase, { useValue: new MockYextActivateLocationUseCase() as any });
        container.register(AddDefaultReviewAutomationsUseCase, { useValue: new MockAddDefaultReviewAutomationsUseCase() as any });
        container.register(RestaurantsDtoMapper, { useValue: new MockRestaurantsDtoMapper() as any });
        container.register(KeywordSearchImpressionsService, { useValue: new MockKeywordSearchImpressionsService() as any });
        container.register(PlatformInsightsRepository, { useValue: new MockPlatformInsightsRepository() as any });
        container.register(ReportsRepository, { useValue: new MockReportsRepository() as any });
        container.register(RestaurantAiSettingsRepository, { useValue: new MockRestaurantAiSettingsRepository() as any });
        container.register(PlatformsRepository, { useValue: new MockPlatformsRepository() as any });
    });

    describe('Hyperline Integration', () => {
        it('should delete restaurant and throw error when subscriptionsProvider fails', async () => {
            // Mock subscriptionsProvider that fails
            const mockSubscriptionsProvider = {
                updateSubscriptionsProviderLocation: jest.fn().mockRejectedValue(new Error('Hyperline API failed')),
            };

            // Mock restaurants repository with spy methods
            const mockRestaurantsRepository = {
                updateOne: jest.fn().mockResolvedValue({}),
                deleteOne: jest.fn().mockResolvedValue({}),
                findOneAndUpdate: jest.fn().mockResolvedValue({}),
            };

            container.register(InjectionToken.SubscriptionsProvider, { useValue: mockSubscriptionsProvider });
            container.register(RestaurantsRepository, { useValue: mockRestaurantsRepository as any });

            const createRestaurantUseCase = container.resolve(CreateRestaurantUseCase);

            const testCase = new TestCaseBuilderV2<'users'>({
                seeds: {
                    users: {
                        data() {
                            return [getDefaultUser().build()];
                        },
                    },
                },
            });
            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const userId = seededObjects.users[0]._id.toString();

            const payload = {
                userId,
                type: BusinessCategory.LOCAL_BUSINESS,
                name: 'Test Restaurant',
                socialId: 'test-place-id',
                credentialId: newDbId().toString(),
                organizationId: newDbId().toString(),
                fromForm: true,
                fromTests: true,
                subscriptionsProviderId: 'test-hyperline-id',
                access: { isValid: true, missing: [] },
                address: {
                    country: 'FR',
                    regionCode: 'FR',
                    formattedAddress: 'Test Address',
                    locality: 'Paris',
                    postalCode: '75001',
                },
                formattedAddress: 'Test Address',
                locationId: 'test-location-id',
                apiEndpointV2: 'https://api.test.com',
                socialUrl: 'https://test.com',
                accountId: 'test-account-id',
                accountName: 'Test Account',
            } as CreateRestaurantBodyDto & { userId: string };

            await expect(createRestaurantUseCase.execute(payload)).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: MalouErrorCode.HYPERLINE_INTEGRATION_ERROR,
                    message: 'Hyperline integration failed',
                })
            );

            // Verify that updateSubscriptionsProviderLocation was called
            expect(mockSubscriptionsProvider.updateSubscriptionsProviderLocation).toHaveBeenCalledWith({
                subscriptionsProviderLocationId: 'test-hyperline-id',
                malouRestaurantId: expect.any(String),
            });

            // Verify that restaurant was deleted after failure
            expect(mockRestaurantsRepository.deleteOne).toHaveBeenCalledWith({
                filter: { _id: expect.any(Object) },
            });

            // Verify that updateOne was NOT called for subscriptionsProviderId (since the subscriptionsProvider failed first)
            expect(mockRestaurantsRepository.updateOne).not.toHaveBeenCalled();
        });

        it('should set subscriptionsProviderId when subscriptionsProvider succeeds', async () => {
            // Mock subscriptionsProvider that succeeds
            const mockSubscriptionsProvider = {
                updateSubscriptionsProviderLocation: jest.fn().mockResolvedValue(undefined),
            };

            // Mock restaurants repository with spy methods
            const mockRestaurantsRepository = {
                updateOne: jest.fn().mockResolvedValue({}),
                deleteOne: jest.fn().mockResolvedValue({}),
                findOneAndUpdate: jest.fn().mockResolvedValue({}),
            };

            container.register(InjectionToken.SubscriptionsProvider, { useValue: mockSubscriptionsProvider });
            container.register(RestaurantsRepository, { useValue: mockRestaurantsRepository as any });

            const createRestaurantUseCase = container.resolve(CreateRestaurantUseCase);

            const testCase = new TestCaseBuilderV2<'users'>({
                seeds: {
                    users: {
                        data() {
                            return [getDefaultUser().build()];
                        },
                    },
                },
            });
            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const userId = seededObjects.users[0]._id.toString();

            const payload = {
                userId,
                type: BusinessCategory.LOCAL_BUSINESS,
                name: 'Test Restaurant',
                socialId: 'test-place-id',
                credentialId: newDbId().toString(),
                organizationId: newDbId().toString(),
                fromForm: true,
                fromTests: true,
                subscriptionsProviderId: 'test-hyperline-id',
                access: { isValid: true, missing: [] },
                address: {
                    country: 'FR',
                    regionCode: 'FR',
                    formattedAddress: 'Test Address',
                    locality: 'Paris',
                    postalCode: '75001',
                },
                formattedAddress: 'Test Address',
                locationId: 'test-location-id',
                apiEndpointV2: 'https://api.test.com',
                socialUrl: 'https://test.com',
                accountId: 'test-account-id',
                accountName: 'Test Account',
            } as CreateRestaurantBodyDto & { userId: string };

            const result = await createRestaurantUseCase.execute(payload);

            // Verify that updateSubscriptionsProviderLocation was called
            expect(mockSubscriptionsProvider.updateSubscriptionsProviderLocation).toHaveBeenCalledWith({
                subscriptionsProviderLocationId: 'test-hyperline-id',
                malouRestaurantId: expect.any(String),
            });

            // Verify that restaurant was updated with subscriptionsProviderId
            expect(mockRestaurantsRepository.updateOne).toHaveBeenCalledWith({
                filter: { _id: expect.any(Object) },
                update: { subscriptionsProviderId: 'test-hyperline-id' },
            });

            // Verify that restaurant was NOT deleted (success scenario)
            expect(mockRestaurantsRepository.deleteOne).not.toHaveBeenCalled();

            // Verify that the result is returned
            expect(result).toBeDefined();
            expect(result).toHaveProperty('_id');
        });
    });
});
